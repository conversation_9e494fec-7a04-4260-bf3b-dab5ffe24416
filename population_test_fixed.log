Loading coordinate and population data with updated hierarchy...
  Priority 1: Loading census data...
    Loaded 5411 coordinates from census data
  Priority 2: Loading gazetteer data...
    Added 79984 coordinates from gazetteer data
  Priority 3: Skipping tessellation data for this test...
  Total coordinates loaded: 85395
  Total population data loaded: 5411

Population dictionary statistics:
  Total tract_populations: 5411
  Census population dict: 5411 entries
  Tessellation population dict: 0 entries
  Sample census tract IDs: ['36001000100', '36001000201', '36001000202', '36001000301', '36001000302']
  Sample census population values: [np.float64(2073.0), np.float64(3125.0), np.float64(2598.0), np.float64(3190.0), np.float64(3496.0)]
  Coordinate source distribution: {'census': 5411, 'gazetteer': 79984}

🧪 Testing population lookup for sample NYC tracts:
   36061003700: 3092.0
   36061008200: 3414.0
   36047082400: 4963.0
   36081068000: 5220.0

✅ Population loading test completed!
