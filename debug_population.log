🔍 DETAILED POPULATION LOOKUP DEBUG
==================================================
📊 Loading census data with exact same logic...
   ✅ Loaded 5411 population entries
   📊 Census population dict: 5411 entries
   📊 Tessellation population dict: 0 entries

🔍 Loading actual tract IDs from input data...
   📋 Sample destination tracts: ['36061008200', '36061011300', '36061007600', '36061009400', '36061001502', '36061010200', '36061009100', '36061010100', '36061004300', '36061001800']

🧪 Testing population lookup for destination tracts:
   ✅ 36061008200: 3414.0
   ✅ 36061011300: 216.0
   ✅ 36061007600: 2921.0
   ✅ 36061009400: 109.0
   ✅ 36061001502: 10378.0
   ✅ 36061010200: 283.0
   ✅ 36061009100: 7362.0
   ✅ 36061010100: 2596.0
   ✅ 36061004300: 4158.0
   ✅ 36061001800: 7934.0
   ✅ 36061029700: 16.0
   ✅ 36061011402: 2620.0
   ❌ 36061009900: NOT FOUND
      🔍 Not in census_population
      🔍 Not in tract_populations either
   ✅ 36061008000: 5906.0
   ✅ 36061005800: 5109.0

📊 Population lookup results: 14/15 found

🔍 Checking for NYC tract IDs in census data...
   📊 NYC tracts in census_population: 310
   📋 Sample NYC tracts: ['36061000100', '36061000201', '36061000202', '36061000500', '36061000600', '36061000700', '36061000800', '36061000900', '36061001001', '36061001002']

🧪 Testing lookup for known NYC tracts:
   ✅ 36061000100: 0.0
   ✅ 36061000201: 2012.0
   ✅ 36061000202: 7266.0
   ✅ 36061000500: 5.0
   ✅ 36061000600: 11616.0

✅ Population lookup is working for some tracts!
