Loading coordinate and population data with updated hierarchy...
  Priority 1: Loading census data...
    Loaded 5411 coordinates from census data
  Priority 2: Loading gazetteer data...
    Added 79984 coordinates from gazetteer data
  Priority 3: Skipping tessellation data for this test...
  Total coordinates loaded: 85395
  Total population data loaded: 5411

Population dictionary statistics:
  Total tract_populations: 5411
  Census population dict: 5411 entries
  Tessellation population dict: 0 entries

Testing with file: NYC-2024-monthly-01.csv.gz
Output will be saved to: monthly_output_files/tract_flows_with_population_2024-month-01_test.csv
Reading data (first 1000 rows for testing)...
Extracting columns...
Processing visitor data...
Found 87033 CBG flows
Aggregating to tract level...
Aggregated to 42110 tract pairs

🧪 Testing population lookup for first 5 tract pairs:
  0: 01003010701 -> 36061011900
    Source pop: None, Dest pop: 1666.0
  1: 01003010800 -> 36061009900
    Source pop: None, Dest pop: None
  2: 01017954300 -> 36061008200
    Source pop: None, Dest pop: 3414.0
  3: 01017954300 -> 36061009600
    Source pop: None, Dest pop: 608.0
  4: 01017954300 -> 36061010400
    Source pop: None, Dest pop: 983.0

✅ Test completed successfully!
