#!/usr/bin/env python3
"""
Monthly Tract Flow Analysis Pipeline

This script processes monthly aggregated input files and generates monthly tract flow output files.
It includes all recent modifications:
- Updated coordinate hierarchy (census → gazetteer → tessellation)
- Multi-polygon centroid calculation for tessellation data
- Destination coordinate source tracking

Modified from process_tract_flows.py for monthly processing.

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import re
import json
import os
import glob
import gzip
from math import radians, sin, cos, sqrt, atan2

# Function to calculate distance between two points using Haversine formula
def haversine_distance(lat1, lon1, lat2, lon2):
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    r = 6371  # Radius of Earth in kilometers

    return r * c  # Distance in kilometers

# Function to parse visitor_home_cbgs with extra quotes
def parse_visitor_home_cbgs(json_str):
    if pd.isna(json_str) or json_str == '{}':
        return {}

    # Use regex to extract CBG and count pairs
    pattern = r'""""(\d+)"""":([\d]+)'
    matches = re.findall(pattern, str(json_str))

    result = {}
    for cbg, count in matches:
        result[cbg] = int(count)

    return result

# Function to get tract population from available data sources
def get_tract_population(tract_id):
    """
    Get the population of a tract from available data sources
    Returns None if population data is not available
    """
    # Try to get from census_population
    if tract_id in census_population:
        return census_population[tract_id]
    # If not found, try to get from tessellation_population
    elif tract_id in tessellation_population:
        return tessellation_population[tract_id]
    # If still not found, return None
    return None

# Function to load coordinate and population data with updated hierarchy
def load_coordinate_and_population_data():
    """
    Load coordinate and population data from all sources using updated 3-tier hierarchy:
    1. Census data (highest priority)
    2. Gazetteer data (medium priority)
    3. Tessellation data (lowest priority)

    Returns dictionaries for coordinates, populations, and coordinate sources
    """
    tract_coords = {}
    tract_populations = {}
    coord_sources = {}

    print("Loading coordinate and population data with updated hierarchy...")

    # Priority 1: Load from census data (highest priority)
    print("  Priority 1: Loading census data...")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_count = 0
        for _, row in census_df.iterrows():
            # Keep tract ID format consistent with working processor - convert to string and remove .0 suffix
            tract_id = str(int(float(row['tract'])))
            if pd.notna(row['latitude']) and pd.notna(row['longitude']):
                tract_coords[tract_id] = {
                    'latitude': row['latitude'],
                    'longitude': row['longitude']
                }
                coord_sources[tract_id] = 'census'
                census_count += 1
            if pd.notna(row['population']):
                tract_populations[tract_id] = row['population']
        print(f"    Loaded {census_count} coordinates from census data")

    # Priority 2: Load from gazetteer data (medium priority - only if not in census)
    print("  Priority 2: Loading gazetteer data...")
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        gazetteer_count = 0
        with open(gaz_file, 'r') as f:
            # Skip header
            next(f)
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]
                    # Only add if not already from census data
                    if tract_id not in tract_coords:
                        try:
                            lat = float(parts[6])
                            lon = float(parts[7])
                            tract_coords[tract_id] = {
                                'latitude': lat,
                                'longitude': lon
                            }
                            coord_sources[tract_id] = 'gazetteer'
                            gazetteer_count += 1
                        except (ValueError, IndexError):
                            continue
        print(f"    Added {gazetteer_count} coordinates from gazetteer data")

    # Priority 3: Skip tessellation data for this test (to speed up processing)
    print("  Priority 3: Skipping tessellation data for this test...")
    tessellation_file = None  # 'population files/tessellation_with_population.geojson'
    if os.path.exists(tessellation_file):
        try:
            with open(tessellation_file, 'r') as f:
                tessellation_data = json.load(f)

            # Phase 1: Collect all polygon centroids per tract ID
            temp_centroids = {}
            tessellation_populations_temp = {}

            for feature in tessellation_data.get('features', []):
                properties = feature.get('properties', {})
                tile_id = properties.get('tile_ID', '')

                if tile_id and len(str(tile_id)) >= 11:
                    # Extract tract ID from tile_ID (first 11 digits)
                    tract_id = str(tile_id)[:11]

                    # Only process if not already found in census or gazetteer
                    if tract_id not in tract_coords:
                        # Calculate polygon centroid
                        geometry = feature.get('geometry', {})
                        if geometry.get('type') == 'Polygon':
                            coords = geometry.get('coordinates', [])[0]
                            if coords and len(coords) > 0:
                                # Validate coordinates
                                valid_coords = []
                                for coord in coords:
                                    if (len(coord) >= 2 and
                                        coord[0] is not None and coord[1] is not None and
                                        not pd.isna(coord[0]) and not pd.isna(coord[1]) and
                                        coord[0] != 0 and coord[1] != 0):
                                        valid_coords.append(coord)

                                if valid_coords:
                                    # Calculate centroid for this polygon
                                    sum_x = sum(p[0] for p in valid_coords)
                                    sum_y = sum(p[1] for p in valid_coords)
                                    count = len(valid_coords)
                                    centroid_lon = sum_x / count
                                    centroid_lat = sum_y / count

                                    # Store in temporary structure
                                    if tract_id not in temp_centroids:
                                        temp_centroids[tract_id] = []
                                    temp_centroids[tract_id].append({
                                        'lat': centroid_lat,
                                        'lon': centroid_lon
                                    })

                    # Collect population data (regardless of coordinate availability)
                    population = properties.get('population')
                    if population is not None and tract_id not in tract_populations:
                        tessellation_populations_temp[tract_id] = float(population)

            # Phase 2: Calculate averaged centroids for tracts with multiple polygons
            tessellation_count = 0
            for tract_id, centroid_list in temp_centroids.items():
                if len(centroid_list) > 1:
                    # Average multiple polygon centroids
                    final_lat = sum(centroid['lat'] for centroid in centroid_list) / len(centroid_list)
                    final_lon = sum(centroid['lon'] for centroid in centroid_list) / len(centroid_list)
                else:
                    # Single polygon case
                    final_lat = centroid_list[0]['lat']
                    final_lon = centroid_list[0]['lon']

                # Validate final coordinates are within reasonable bounds
                if (-90 <= final_lat <= 90 and -180 <= final_lon <= 180):
                    tract_coords[tract_id] = {
                        'latitude': final_lat,
                        'longitude': final_lon
                    }
                    coord_sources[tract_id] = 'tessellation'
                    tessellation_count += 1

            # Add tessellation population data
            for tract_id, population in tessellation_populations_temp.items():
                if tract_id not in tract_populations:
                    tract_populations[tract_id] = population

            print(f"    Added {tessellation_count} coordinates from tessellation data")
            print(f"    Processed {len(temp_centroids)} tracts with {sum(len(centroids) for centroids in temp_centroids.values())} total polygons")

        except Exception as e:
            print(f"    Error loading tessellation data: {e}")

    print(f"  Total coordinates loaded: {len(tract_coords)}")
    print(f"  Total population data loaded: {len(tract_populations)}")

    return tract_coords, tract_populations, coord_sources

# Create output directory if it doesn't exist
os.makedirs('monthly_output_files', exist_ok=True)

# Load coordinate and population data using the new function
tract_coords, tract_populations, coord_sources = load_coordinate_and_population_data()

# For backward compatibility, create separate dictionaries
census_coords = {k: v for k, v in tract_coords.items() if coord_sources.get(k) == 'census'}
gaz_coords = {k: v for k, v in tract_coords.items() if coord_sources.get(k) == 'gazetteer'}
tessellation_coords = {k: v for k, v in tract_coords.items() if coord_sources.get(k) == 'tessellation'}

# Create separate population dictionaries for backward compatibility
census_population = {}
tessellation_population = {}
for tract_id, population in tract_populations.items():
    source = coord_sources.get(tract_id, 'unknown')
    if source == 'census':
        census_population[tract_id] = population
    elif source == 'tessellation':
        tessellation_population[tract_id] = population

# Debug: Print population dictionary statistics
print(f"\nPopulation dictionary statistics:")
print(f"  Total tract_populations: {len(tract_populations)}")
print(f"  Census population dict: {len(census_population)} entries")
print(f"  Tessellation population dict: {len(tessellation_population)} entries")

# Debug: Show sample tract IDs from each population source
if census_population:
    sample_census = list(census_population.keys())[:5]
    print(f"  Sample census tract IDs: {sample_census}")
    # Show sample population values
    sample_pop_values = [census_population[tid] for tid in sample_census]
    print(f"  Sample census population values: {sample_pop_values}")
if tessellation_population:
    sample_tessellation = list(tessellation_population.keys())[:5]
    print(f"  Sample tessellation tract IDs: {sample_tessellation}")

# Debug: Show coordinate source distribution
source_distribution = {}
for source in coord_sources.values():
    source_distribution[source] = source_distribution.get(source, 0) + 1
print(f"  Coordinate source distribution: {source_distribution}")

# Debug: Test population lookup for a few known NYC tracts
test_tracts = ['36061003700', '36061008200', '36047082400', '36081068000']
print(f"\n🧪 Testing population lookup for sample NYC tracts:")
for tract in test_tracts:
    pop = get_tract_population(tract)
    print(f"   {tract}: {pop}")
    if pop is None:
        # Check if tract exists in either dictionary
        in_census = tract in census_population
        in_tess = tract in tessellation_population
        print(f"     (in census: {in_census}, in tessellation: {in_tess})")

# Get all input files
input_files = glob.glob('NYC-2024-monthly-aggregated/*.csv.gz')
print(f"Found {len(input_files)} input files to process.")

# For testing, process just the first file
if input_files:
    input_files = [input_files[0]]
    print(f"Testing with just the first file: {os.path.basename(input_files[0])}")

# Process each input file
for input_file in input_files:
    # Extract the month from the filename
    file_basename = os.path.basename(input_file)
    date_match = re.search(r'NYC-2024-monthly-(\d{2})', file_basename)
    if date_match:
        month_str = date_match.group(1)
        date_str = f'2024-month-{month_str}'
    else:
        date_str = "unknown_date"

    output_file = f"monthly_output_files/tract_flows_with_population_{date_str}.csv"
    print(f"\nProcessing file: {file_basename}")
    print(f"Output will be saved to: {output_file}")

    # Read the CSV file
    print("Reading data...")
    try:
        # Read gzipped CSV file with UTF-8 encoding and error handling
        with gzip.open(input_file, 'rt', encoding='utf-8', errors='replace') as f:
            df = pd.read_csv(f)

        # Extract necessary columns and create a copy to avoid SettingWithCopyWarning
        print("Extracting columns...")
        data = df[['placekey', 'poi_cbg', 'visitor_home_cbgs', 'raw_visit_counts', 'latitude', 'longitude']].copy()

        # Create a list to store source-destination pairs at CBG level
        print("Processing visitor data...")
        cbg_flows = []

        # Process each row
        for _, row in data.iterrows():
            if pd.isna(row['poi_cbg']) or pd.isna(row['visitor_home_cbgs']):
                continue

            poi_cbg = str(row['poi_cbg'])  # Convert to string
            visitor_home_cbgs = parse_visitor_home_cbgs(row['visitor_home_cbgs'])

            # Create source-destination pairs
            for source_cbg, flow in visitor_home_cbgs.items():
                if pd.isna(row['latitude']) or pd.isna(row['longitude']):
                    continue

                cbg_flows.append({
                    'source_cbg': str(source_cbg),
                    'destination_cbg': poi_cbg,
                    'flow': flow,
                    'dest_lat': row['latitude'],
                    'dest_lon': row['longitude']
                })

        # Convert to DataFrame
        cbg_flow_df = pd.DataFrame(cbg_flows)

        # Check if we have any flows
        if len(cbg_flow_df) == 0:
            print("No valid flows found in the data. Skipping this file.")
            continue

        # Skip saving CBG level flows as we only need tract-level output
        print("Proceeding to tract-level aggregation...")

        # Convert CBG to tract by taking the first 11 digits
        print("Aggregating to tract level...")
        cbg_flow_df['source_tract'] = cbg_flow_df['source_cbg'].astype(str).str[:11]
        cbg_flow_df['destination_tract'] = cbg_flow_df['destination_cbg'].astype(str).str[:11]

        # Aggregate flows to tract level
        tract_flow_df = cbg_flow_df.groupby(['source_tract', 'destination_tract']).agg({
            'flow': 'sum',
            'dest_lat': 'first',
            'dest_lon': 'first'
        }).reset_index()

        # Create a mapping of tracts to their POIs (placekeys)
        print("Creating tract to POI mapping...")
        # Use .loc to avoid SettingWithCopyWarning
        data.loc[:, 'tract'] = data['poi_cbg'].astype(str).str[:11]
        tract_to_pois = data.groupby('tract')['placekey'].apply(list).to_dict()

        # Calculate buffer for each source-destination pair
        print("Calculating buffer and source-destination distances...")
        tract_flow_df['buffer'] = 0
        tract_flow_df['Source_Destination_Distance'] = 0.0  # Add new column for distance as float
        tract_flow_df['source_lat'] = 0.0  # Add new column for source latitude
        tract_flow_df['source_lon'] = 0.0  # Add new column for source longitude
        tract_flow_df['dest_lat'] = tract_flow_df['dest_lat']  # Already have destination latitude
        tract_flow_df['dest_lon'] = tract_flow_df['dest_lon']  # Already have destination longitude
        tract_flow_df['Source_Population'] = None  # Add new column for source population
        tract_flow_df['Destination_Population'] = None  # Add new column for destination population
        tract_flow_df['population_within_circle'] = 0  # Add new column for population within circle
        tract_flow_df['missing_population_tracts'] = 0  # Add new column for number of tracts with missing population data
        tract_flow_df['source_coord_source'] = ''  # Add new column for source coordinate source tracking
        tract_flow_df['dest_coord_source'] = ''  # Add new column for destination coordinate source tracking

        # Initialize coordinate source tracking counters
        coord_source_stats = {
            'census': 0,
            'tessellation': 0,
            'gazetteer': 0,
            'not_found': 0
        }
        tracts_not_found = []

        # Debug: Show sample tract IDs from input data
        if len(tract_flow_df) > 0:
            sample_source_tracts = tract_flow_df['source_tract'].unique()[:5]
            sample_dest_tracts = tract_flow_df['destination_tract'].unique()[:5]
            print(f"  Sample input source tracts: {sample_source_tracts.tolist()}")
            print(f"  Sample input destination tracts: {sample_dest_tracts.tolist()}")

        # Calculate buffer for each source-destination pair
        for idx, row in tract_flow_df.iterrows():
            source_tract = row['source_tract']
            dest_tract = row['destination_tract']

            # Debug: Show first few population lookups
            if idx < 5:
                print(f"  Debug row {idx}: source_tract={source_tract}, dest_tract={dest_tract}")
                print(f"    Source in census_population: {source_tract in census_population}")
                print(f"    Source in tessellation_population: {source_tract in tessellation_population}")
                print(f"    Dest in census_population: {dest_tract in census_population}")
                print(f"    Dest in tessellation_population: {dest_tract in tessellation_population}")

                # Test actual population lookup
                source_pop_test = get_tract_population(source_tract)
                dest_pop_test = get_tract_population(dest_tract)
                print(f"    Source population lookup result: {source_pop_test}")
                print(f"    Dest population lookup result: {dest_pop_test}")

            # Get destination coordinates
            dest_lat = row['dest_lat']
            dest_lon = row['dest_lon']

            # Get source coordinates
            source_lat = 0.0
            source_lon = 0.0

            # Try to get coordinates from different sources in updated 3-tier hierarchy
            coord_source = 'not_found'

            # 1. First priority: census_coords (from census-populations-2020-tract-new-york.csv)
            if source_tract in census_coords:
                source_lat = census_coords[source_tract]['latitude']
                source_lon = census_coords[source_tract]['longitude']
                coord_source = 'census'
                coord_source_stats['census'] += 1

            # 2. Second priority: gaz_coords (from 2020_Gaz_tracts_national.txt) - PROMOTED
            elif source_tract in gaz_coords:
                source_lat = gaz_coords[source_tract]['latitude']
                source_lon = gaz_coords[source_tract]['longitude']
                coord_source = 'gazetteer'
                coord_source_stats['gazetteer'] += 1

            # 3. Third priority: tessellation_coords (from tessellation_with_population.geojson) - DEMOTED
            elif source_tract in tessellation_coords:
                source_lat = tessellation_coords[source_tract]['latitude']
                source_lon = tessellation_coords[source_tract]['longitude']
                coord_source = 'tessellation'
                coord_source_stats['tessellation'] += 1

            # 4. Fallback: coordinates remain 0.0 if tract not found in any source
            else:
                coord_source_stats['not_found'] += 1
                tracts_not_found.append(source_tract)

            # Save the coordinates and coordinate source in the new columns
            tract_flow_df.at[idx, 'source_lat'] = source_lat
            tract_flow_df.at[idx, 'source_lon'] = source_lon
            tract_flow_df.at[idx, 'source_coord_source'] = coord_source

            # Get destination coordinates and track source using same hierarchy
            dest_coord_source = 'not_found'

            # Try to get destination coordinates using same 3-tier hierarchy
            # 1. First priority: census_coords
            if dest_tract in census_coords:
                # Update destination coordinates from census data if available
                dest_lat = census_coords[dest_tract]['latitude']
                dest_lon = census_coords[dest_tract]['longitude']
                dest_coord_source = 'census'

            # 2. Second priority: gaz_coords
            elif dest_tract in gaz_coords:
                # Update destination coordinates from gazetteer data if available
                dest_lat = gaz_coords[dest_tract]['latitude']
                dest_lon = gaz_coords[dest_tract]['longitude']
                dest_coord_source = 'gazetteer'

            # 3. Third priority: tessellation_coords
            elif dest_tract in tessellation_coords:
                # Update destination coordinates from tessellation data if available
                dest_lat = tessellation_coords[dest_tract]['latitude']
                dest_lon = tessellation_coords[dest_tract]['longitude']
                dest_coord_source = 'tessellation'

            # 4. Fallback: use original POI coordinates (already in dest_lat, dest_lon)
            else:
                dest_coord_source = 'poi_original'

            # Update destination coordinates and source tracking
            tract_flow_df.at[idx, 'dest_lat'] = dest_lat
            tract_flow_df.at[idx, 'dest_lon'] = dest_lon
            tract_flow_df.at[idx, 'dest_coord_source'] = dest_coord_source

            # Get source population
            source_population = None
            # Try to get population from census_population
            if source_tract in census_population:
                source_population = census_population[source_tract]
            # If not found, try to get from tessellation_population
            elif source_tract in tessellation_population:
                source_population = tessellation_population[source_tract]
            # Save the source population
            tract_flow_df.at[idx, 'Source_Population'] = source_population

            # Get destination population
            dest_tract = row['destination_tract']
            dest_population = None
            # Try to get population from census_population
            if dest_tract in census_population:
                dest_population = census_population[dest_tract]
            # If not found, try to get from tessellation_population
            elif dest_tract in tessellation_population:
                dest_population = tessellation_population[dest_tract]
            # Save the destination population
            tract_flow_df.at[idx, 'Destination_Population'] = dest_population

            # Calculate distance between source and destination only if we have valid coordinates
            if source_lat != 0.0 and source_lon != 0.0 and not pd.isna(source_lat) and not pd.isna(source_lon) and not pd.isna(dest_lat) and not pd.isna(dest_lon):
                distance = haversine_distance(source_lat, source_lon, dest_lat, dest_lon)
                # Save the distance in the new column
                tract_flow_df.at[idx, 'Source_Destination_Distance'] = distance

            # Count POIs within the circle centered at destination and touching source
            # excluding POIs in source or destination
            buffer_count = 0
            population_within_circle = 0
            missing_population_tracts = 0

            # Only calculate buffer and population within circle if we have valid coordinates and a valid distance
            if source_lat != 0.0 and source_lon != 0.0 and not pd.isna(source_lat) and not pd.isna(source_lon) and not pd.isna(dest_lat) and not pd.isna(dest_lon) and 'distance' in locals():
                for tract, pois in tract_to_pois.items():
                    # Skip source and destination tracts
                    if tract == source_tract or tract == dest_tract:
                        continue

                    # Check if this tract has POIs within the buffer distance
                    # Use the same coordinate hierarchy for buffer tract coordinates
                    tract_lat = 0.0
                    tract_lon = 0.0

                    # Try to get coordinates using the updated 3-tier hierarchy
                    if tract in census_coords:
                        tract_lat = census_coords[tract]['latitude']
                        tract_lon = census_coords[tract]['longitude']
                    elif tract in gaz_coords:
                        tract_lat = gaz_coords[tract]['latitude']
                        tract_lon = gaz_coords[tract]['longitude']
                    elif tract in tessellation_coords:
                        tract_lat = tessellation_coords[tract]['latitude']
                        tract_lon = tessellation_coords[tract]['longitude']

                    # Only proceed if we found valid coordinates
                    if tract_lat != 0.0 and tract_lon != 0.0 and not pd.isna(tract_lat) and not pd.isna(tract_lon):

                        # Calculate distance from destination to this tract
                        tract_distance = haversine_distance(dest_lat, dest_lon, tract_lat, tract_lon)

                        # If distance is less than or equal to the buffer distance, count POIs and population
                        if tract_distance <= distance:
                            # Add to buffer count
                            buffer_count += len(pois)

                            # Get tract population
                            tract_population = get_tract_population(tract)

                            # Add to population within circle if available
                            if tract_population is not None:
                                population_within_circle += tract_population
                            else:
                                # Increment missing data counter
                                missing_population_tracts += 1

            # Save the calculated values
            tract_flow_df.at[idx, 'buffer'] = buffer_count
            tract_flow_df.at[idx, 'population_within_circle'] = population_within_circle
            tract_flow_df.at[idx, 'missing_population_tracts'] = missing_population_tracts

        # Print coordinate source usage statistics
        print("\nCoordinate source usage statistics:")
        print(f"  Census coordinates: {coord_source_stats['census']} tracts")
        print(f"  Tessellation coordinates: {coord_source_stats['tessellation']} tracts")
        print(f"  Gazetteer coordinates: {coord_source_stats['gazetteer']} tracts")
        print(f"  Not found in any source: {coord_source_stats['not_found']} tracts")

        if tracts_not_found:
            print(f"\nTracts not found in any coordinate source (first 10): {tracts_not_found[:10]}")
            if len(tracts_not_found) > 10:
                print(f"  ... and {len(tracts_not_found) - 10} more")

        # Select and rename columns for tract level output
        print("Preparing tract level output...")
        final_df = tract_flow_df[['source_tract', 'destination_tract', 'flow', 'buffer', 'Source_Destination_Distance',
                                'source_lat', 'source_lon', 'dest_lat', 'dest_lon',
                                'Source_Population', 'Destination_Population',
                                'population_within_circle', 'missing_population_tracts', 'source_coord_source', 'dest_coord_source']]
        final_df.columns = ['Source', 'Destination', 'AggFlow', 'Buffer', 'Source_Destination_Distance',
                            'Source_Latitude', 'Source_Longitude', 'Destination_Latitude', 'Destination_Longitude',
                            'Source_Population', 'Destination_Population',
                            'Population_within_Circle', 'Missing_Population_Tracts', 'Source_Coordinate_Source', 'Destination_Coordinate_Source']

        # Save tract level flows to CSV
        print(f"Saving tract level output to {output_file}...")
        final_df.to_csv(output_file, index=False)

        print(f"Done! Tract-level flows for {date_str} have been saved to {output_file}")

        # Print sample of tract level flows
        print("\nSample of tract level flows:")
        print(final_df.head())

        # Check if there are any non-zero buffer values
        non_zero_buffers = final_df[final_df['Buffer'] > 0]
        print(f"\nNumber of tract pairs with non-zero buffer values: {len(non_zero_buffers)}")
        if len(non_zero_buffers) > 0:
            print("\nSample of tract pairs with non-zero buffer values:")
            print(non_zero_buffers.head())

        # Check population calculation statistics
        non_null_source_pop = final_df[final_df['Source_Population'].notna()]
        non_null_dest_pop = final_df[final_df['Destination_Population'].notna()]
        non_zero_pop_within_circle = final_df[final_df['Population_within_Circle'] > 0]

        print(f"\n📊 Population Calculation Statistics:")
        print(f"  Total tract pairs: {len(final_df)}")
        print(f"  Pairs with source population: {len(non_null_source_pop)} ({len(non_null_source_pop)/len(final_df)*100:.1f}%)")
        print(f"  Pairs with destination population: {len(non_null_dest_pop)} ({len(non_null_dest_pop)/len(final_df)*100:.1f}%)")
        print(f"  Pairs with non-zero population within circle: {len(non_zero_pop_within_circle)} ({len(non_zero_pop_within_circle)/len(final_df)*100:.1f}%)")

        if len(non_zero_pop_within_circle) > 0:
            avg_pop_within_circle = non_zero_pop_within_circle['Population_within_Circle'].mean()
            max_pop_within_circle = non_zero_pop_within_circle['Population_within_Circle'].max()
            print(f"  Average population within circle (non-zero): {avg_pop_within_circle:.0f}")
            print(f"  Maximum population within circle: {max_pop_within_circle:.0f}")

    except Exception as e:
        print(f"Error processing file {input_file}: {e}")
        continue

print("\nAll files have been processed. Results are saved in the monthly_output_files directory.")
